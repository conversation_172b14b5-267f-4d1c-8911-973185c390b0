"""
Pydantic data models for OLED Display UI Designer project system.
Defines the structure for projects, canvases, assets, and configuration.
"""

from datetime import datetime
from typing import List, Dict, Any, Literal, Optional
from pathlib import Path
import json
import zlib
import base64

from pydantic import BaseModel, Field, validator


class Asset(BaseModel):
    """Represents a bitmap asset (icon) in the project."""
    name: str = Field(..., description="Asset name")
    width: int = Field(..., gt=0, le=128, description="Asset width in pixels")
    height: int = Field(..., gt=0, le=64, description="Asset height in pixels")
    bytes_data: bytes = Field(..., description="Packed bitmap bytes")
    order: Literal["row_msb", "row_lsb", "col_msb", "col_lsb"] = Field(
        default="row_msb", description="Bit packing order"
    )
    created_at: datetime = Field(default_factory=datetime.now)
    tags: List[str] = Field(default_factory=list, description="Asset tags for organization")
    
    class Config:
        # Handle bytes serialization
        json_encoders = {
            bytes: lambda v: base64.b64encode(v).decode('utf-8'),
            datetime: lambda v: v.isoformat()
        }
    
    @validator('bytes_data', pre=True)
    def decode_bytes(cls, v):
        """Handle base64 encoded bytes during deserialization."""
        if isinstance(v, str):
            return base64.b64decode(v.encode('utf-8'))
        return v


class CanvasModel(BaseModel):
    """Represents a drawing canvas in the project."""
    name: str = Field(..., description="Canvas name")
    width: int = Field(..., gt=0, le=128, description="Canvas width in pixels")
    height: int = Field(..., gt=0, le=64, description="Canvas height in pixels")
    pixels: List[List[int]] = Field(..., description="2D pixel array (0/1 values)")
    origin: Literal["TL", "TR", "BL", "BR"] = Field(
        default="TL", description="Drawing origin corner"
    )
    rotation: Literal[0, 90, 180, 270] = Field(
        default=0, description="Canvas rotation in degrees"
    )
    grid_enabled: bool = Field(default=True, description="Show grid")
    grid_size: int = Field(default=1, ge=1, le=8, description="Grid size in pixels")
    snap_to_grid: bool = Field(default=False, description="Snap drawing to grid")
    
    @validator('pixels')
    def validate_pixels(cls, v, values):
        """Ensure pixels array matches canvas dimensions."""
        if 'height' in values and 'width' in values:
            expected_height = values['height']
            expected_width = values['width']
            
            if len(v) != expected_height:
                raise ValueError(f"Pixels height {len(v)} doesn't match canvas height {expected_height}")
            
            for row_idx, row in enumerate(v):
                if len(row) != expected_width:
                    raise ValueError(f"Pixels row {row_idx} width {len(row)} doesn't match canvas width {expected_width}")
                
                # Ensure all values are 0 or 1
                for col_idx, pixel in enumerate(row):
                    if pixel not in (0, 1):
                        raise ValueError(f"Invalid pixel value {pixel} at ({row_idx}, {col_idx}). Must be 0 or 1.")
        
        return v
    
    def create_empty_pixels(self) -> List[List[int]]:
        """Create an empty pixel array for this canvas size."""
        return [[0 for _ in range(self.width)] for _ in range(self.height)]


class Target(BaseModel):
    """OLED display target configuration."""
    display: Literal[
        "128x64_SH1106", "128x64_SSD1306", "96x64_SH1106", "96x64_SSD1306"
    ] = Field(default="128x64_SSD1306", description="Target OLED display type")
    i2c_address: int = Field(default=0x3C, description="I2C address (hex)")
    library: Literal["U8g2", "Adafruit_SSD1306"] = Field(
        default="U8g2", description="Arduino library to use"
    )
    board: Literal["UNO", "Nano", "ESP8266", "ESP32"] = Field(
        default="ESP32", description="Target Arduino board"
    )
    
    @validator('i2c_address')
    def validate_i2c_address(cls, v):
        """Validate I2C address is in valid range."""
        if not (0x08 <= v <= 0x77):
            raise ValueError(f"I2C address 0x{v:02X} is outside valid range (0x08-0x77)")
        return v


class ExportOptions(BaseModel):
    """Export configuration options."""
    bit_order: Literal["row_msb", "row_lsb", "col_msb", "col_lsb"] = Field(
        default="row_msb", description="Bit packing order"
    )
    origin: Literal["TL", "TR", "BL", "BR"] = Field(
        default="TL", description="Drawing origin for export"
    )
    rotation: Literal[0, 90, 180, 270] = Field(
        default=0, description="Export rotation"
    )
    include_assets: bool = Field(default=True, description="Include assets in export")
    generate_header: bool = Field(default=True, description="Generate .h header file")
    generate_sketch: bool = Field(default=True, description="Generate Arduino .ino sketch")
    generate_bitmap: bool = Field(default=True, description="Generate bitmap files")
    variable_prefix: str = Field(default="bitmap", description="Variable name prefix")


class WorkspaceLayout(BaseModel):
    """Workspace layout configuration."""
    window_geometry: Optional[Dict[str, int]] = Field(default=None, description="Main window geometry")
    splitter_sizes: Dict[str, List[int]] = Field(default_factory=dict, description="Splitter panel sizes")
    dock_visibility: Dict[str, bool] = Field(default_factory=dict, description="Dock panel visibility")
    zoom_level: float = Field(default=4.0, ge=0.5, le=32.0, description="Canvas zoom level")
    recent_files: List[str] = Field(default_factory=list, description="Recently opened files")


class Project(BaseModel):
    """Main project model containing all project data."""
    version: str = Field(default="1.0.0", description="Project format version")
    name: str = Field(..., description="Project name")
    created_at: datetime = Field(default_factory=datetime.now)
    modified_at: datetime = Field(default_factory=datetime.now)
    
    canvases: List[CanvasModel] = Field(default_factory=list, description="Project canvases")
    active_canvas: int = Field(default=0, ge=0, description="Index of active canvas")
    assets: List[Asset] = Field(default_factory=list, description="Project assets")
    
    target: Target = Field(default_factory=Target, description="Target OLED configuration")
    export_options: ExportOptions = Field(default_factory=ExportOptions, description="Export settings")
    workspace: WorkspaceLayout = Field(default_factory=WorkspaceLayout, description="UI layout settings")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }
    
    @validator('active_canvas')
    def validate_active_canvas(cls, v, values):
        """Ensure active canvas index is valid."""
        if 'canvases' in values and values['canvases']:
            if v >= len(values['canvases']):
                raise ValueError(f"Active canvas index {v} is out of range")
        return v
    
    def get_active_canvas(self) -> Optional[CanvasModel]:
        """Get the currently active canvas."""
        if self.canvases and 0 <= self.active_canvas < len(self.canvases):
            return self.canvases[self.active_canvas]
        return None
    
    def add_canvas(self, canvas: CanvasModel) -> int:
        """Add a new canvas and return its index."""
        self.canvases.append(canvas)
        self.modified_at = datetime.now()
        return len(self.canvases) - 1
    
    def remove_canvas(self, index: int) -> bool:
        """Remove canvas at index. Returns True if successful."""
        if 0 <= index < len(self.canvases):
            self.canvases.pop(index)
            # Adjust active canvas if needed
            if self.active_canvas >= len(self.canvases):
                self.active_canvas = max(0, len(self.canvases) - 1)
            self.modified_at = datetime.now()
            return True
        return False
    
    def save_to_file(self, file_path: Path, compress: bool = True) -> None:
        """Save project to .skrui file."""
        self.modified_at = datetime.now()
        
        # Serialize to JSON
        json_data = self.json(indent=2)
        
        if compress:
            # Compress with zlib
            compressed_data = zlib.compress(json_data.encode('utf-8'))
            with open(file_path, 'wb') as f:
                f.write(compressed_data)
        else:
            # Save as plain JSON
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(json_data)
    
    @classmethod
    def load_from_file(cls, file_path: Path) -> 'Project':
        """Load project from .skrui file."""
        with open(file_path, 'rb') as f:
            data = f.read()
        
        try:
            # Try to decompress first
            json_data = zlib.decompress(data).decode('utf-8')
        except zlib.error:
            # If decompression fails, assume it's plain JSON
            json_data = data.decode('utf-8')
        
        return cls.parse_raw(json_data)
    
    @classmethod
    def create_default(cls, name: str = "New Project") -> 'Project':
        """Create a new project with default settings."""
        # Create default canvas
        default_canvas = CanvasModel(
            name="Main Canvas",
            width=128,
            height=64,
            pixels=[[0 for _ in range(128)] for _ in range(64)]
        )
        
        return cls(
            name=name,
            canvases=[default_canvas],
            active_canvas=0
        )
