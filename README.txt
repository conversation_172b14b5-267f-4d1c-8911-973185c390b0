OLED Display UI Designer
========================

A professional Windows desktop application for designing pixel-perfect OLED displays.
Developed by SK Raiha | SKR Electronics Lab

Features:
- Pixel-perfect OLED design with drawing tools and shapes
- Image import with advanced monochrome conversion
- Live preview with accurate OLED simulation
- Export to Arduino code (U8g2, Adafruit_SSD1306)
- Support for 128x64 and 96x64 OLED displays
- Professional dark blue UI with resizable panels

Requirements:
- Windows 10/11
- Python 3.11+ (for development)

Development Setup:
1. Create virtual environment:
   python -m venv venv

2. Activate virtual environment:
   venv\Scripts\activate

3. Install dependencies:
   pip install -r requirements.txt

4. Run application:
   python -m app.main

Building Installer:
1. Build with PyInstaller:
   pyinstaller installers/build.spec

2. Create installer with Inno Setup:
   ISCC installers/inno_script.iss

License:
All rights reserved. Proprietary software.
