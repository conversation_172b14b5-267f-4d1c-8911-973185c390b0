"""
Main application class for OLED Display UI Designer.
Handles application initialization, settings, and theme loading.
"""

import logging
from pathlib import Path
from typing import Optional

from PySide6.QtWidgets import QApplication, QMessageBox
from PySide6.QtCore import QSettings, QTimer
from PySide6.QtGui import QFont

from app.window import MainWindow
from app.core.project import Project
from app.utils.qss_loader import load_stylesheet


class OLEDDesignerApp:
    """
    Main application class that manages the OLED Display UI Designer.
    Handles application lifecycle, settings, and main window.
    """
    
    def __init__(self):
        """Initialize the OLED Designer application."""
        self.logger = logging.getLogger(__name__)
        self.settings = QSettings()
        self.main_window: Optional[MainWindow] = None
        self.current_project: Optional[Project] = None
        self.autosave_timer = QTimer()
        
        # Initialize application
        self._setup_application()
        self._setup_autosave()
        self._create_main_window()
        
        self.logger.info("OLED Designer application initialized")
    
    def _setup_application(self) -> None:
        """Setup application-wide settings and theme."""
        app = QApplication.instance()
        
        # Set application font
        font = QFont("Segoe UI", 9)
        app.setFont(font)
        
        # Load and apply dark blue theme
        try:
            stylesheet = load_stylesheet()
            app.setStyleSheet(stylesheet)
            self.logger.info("Dark blue theme loaded successfully")
        except Exception as e:
            self.logger.error(f"Failed to load theme: {e}")
            QMessageBox.warning(
                None,
                "Theme Error",
                f"Failed to load application theme:\n{e}\n\nUsing default theme."
            )
    
    def _setup_autosave(self) -> None:
        """Setup automatic project saving."""
        # Get autosave interval from settings (default: 2 minutes)
        autosave_interval = self.settings.value("autosave_interval", 120000, type=int)  # milliseconds
        
        self.autosave_timer.timeout.connect(self._autosave_project)
        self.autosave_timer.start(autosave_interval)
        
        self.logger.info(f"Autosave enabled with {autosave_interval/1000:.0f} second interval")
    
    def _create_main_window(self) -> None:
        """Create and setup the main application window."""
        self.main_window = MainWindow(self)
        
        # Restore window geometry from settings
        geometry = self.settings.value("window_geometry")
        if geometry:
            self.main_window.restoreGeometry(geometry)
        
        # Restore window state (dock positions, etc.)
        state = self.settings.value("window_state")
        if state:
            self.main_window.restoreState(state)
        
        self.logger.info("Main window created and configured")
    
    def show(self) -> None:
        """Show the main application window."""
        if self.main_window:
            self.main_window.show()
            
            # Create a new default project if none exists
            if not self.current_project:
                self.new_project()
    
    def new_project(self, name: str = "New Project") -> None:
        """Create a new project."""
        try:
            # Check if current project needs saving
            if self.current_project and self._project_has_unsaved_changes():
                if not self._prompt_save_changes():
                    return  # User cancelled
            
            # Create new project
            self.current_project = Project.create_default(name)
            
            # Update main window
            if self.main_window:
                self.main_window.set_project(self.current_project)
                self.main_window.update_window_title()
            
            self.logger.info(f"New project created: {name}")
            
        except Exception as e:
            self.logger.error(f"Failed to create new project: {e}")
            QMessageBox.critical(
                self.main_window,
                "Error",
                f"Failed to create new project:\n{e}"
            )
    
    def open_project(self, file_path: str) -> bool:
        """
        Open a project from file.
        
        Args:
            file_path: Path to the .skrui project file
            
        Returns:
            True if project was opened successfully
        """
        try:
            # Check if current project needs saving
            if self.current_project and self._project_has_unsaved_changes():
                if not self._prompt_save_changes():
                    return False  # User cancelled
            
            # Load project from file
            project_path = Path(file_path)
            if not project_path.exists():
                raise FileNotFoundError(f"Project file not found: {file_path}")
            
            if project_path.suffix.lower() != '.skrui':
                raise ValueError("Invalid file type. Expected .skrui file.")
            
            self.current_project = Project.load_from_file(project_path)
            
            # Update main window
            if self.main_window:
                self.main_window.set_project(self.current_project)
                self.main_window.set_project_file_path(str(project_path))
                self.main_window.update_window_title()
            
            # Add to recent files
            self._add_to_recent_files(str(project_path))
            
            self.logger.info(f"Project opened: {file_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to open project {file_path}: {e}")
            QMessageBox.critical(
                self.main_window,
                "Error",
                f"Failed to open project:\n{e}"
            )
            return False
    
    def save_project(self, file_path: Optional[str] = None) -> bool:
        """
        Save the current project.
        
        Args:
            file_path: Optional path to save to. If None, uses current project path.
            
        Returns:
            True if project was saved successfully
        """
        if not self.current_project:
            return False
        
        try:
            # Determine save path
            if file_path is None:
                if self.main_window:
                    file_path = self.main_window.get_project_file_path()
                
                if not file_path:
                    # No path set, prompt for Save As
                    return self.save_project_as()
            
            # Save project
            save_path = Path(file_path)
            self.current_project.save_to_file(save_path)
            
            # Update main window
            if self.main_window:
                self.main_window.set_project_file_path(str(save_path))
                self.main_window.update_window_title()
                self.main_window.set_project_modified(False)
            
            # Add to recent files
            self._add_to_recent_files(str(save_path))
            
            self.logger.info(f"Project saved: {file_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to save project to {file_path}: {e}")
            QMessageBox.critical(
                self.main_window,
                "Error",
                f"Failed to save project:\n{e}"
            )
            return False
    
    def save_project_as(self) -> bool:
        """Prompt user to save project with a new name."""
        if not self.main_window:
            return False
        
        return self.main_window.save_project_as()
    
    def _autosave_project(self) -> None:
        """Automatically save the current project."""
        if not self.current_project or not self._project_has_unsaved_changes():
            return
        
        try:
            # Create autosave directory if it doesn't exist
            autosave_dir = Path("autosave")
            autosave_dir.mkdir(exist_ok=True)
            
            # Generate autosave filename
            autosave_path = autosave_dir / f"{self.current_project.name}_autosave.skrui"
            
            # Save project
            self.current_project.save_to_file(autosave_path)
            self.logger.debug(f"Project autosaved to: {autosave_path}")
            
        except Exception as e:
            self.logger.error(f"Autosave failed: {e}")
    
    def _project_has_unsaved_changes(self) -> bool:
        """Check if the current project has unsaved changes."""
        if self.main_window:
            return self.main_window.is_project_modified()
        return False
    
    def _prompt_save_changes(self) -> bool:
        """
        Prompt user to save changes before closing/opening another project.
        
        Returns:
            True if user chose to save or discard changes, False if cancelled
        """
        if not self.main_window:
            return True
        
        result = QMessageBox.question(
            self.main_window,
            "Unsaved Changes",
            "The current project has unsaved changes.\n\n"
            "Do you want to save your changes?",
            QMessageBox.Save | QMessageBox.Discard | QMessageBox.Cancel,
            QMessageBox.Save
        )
        
        if result == QMessageBox.Save:
            return self.save_project()
        elif result == QMessageBox.Discard:
            return True
        else:  # Cancel
            return False
    
    def _add_to_recent_files(self, file_path: str) -> None:
        """Add file to recent files list."""
        recent_files = self.settings.value("recent_files", [], type=list)
        
        # Remove if already in list
        if file_path in recent_files:
            recent_files.remove(file_path)
        
        # Add to beginning
        recent_files.insert(0, file_path)
        
        # Limit to 10 recent files
        recent_files = recent_files[:10]
        
        # Save to settings
        self.settings.setValue("recent_files", recent_files)
        
        # Update main window menu
        if self.main_window:
            self.main_window.update_recent_files_menu()
    
    def get_recent_files(self) -> list:
        """Get list of recent files."""
        return self.settings.value("recent_files", [], type=list)
    
    def closeEvent(self, event) -> None:
        """Handle application close event."""
        # Check for unsaved changes
        if self.current_project and self._project_has_unsaved_changes():
            if not self._prompt_save_changes():
                event.ignore()
                return
        
        # Save window geometry and state
        if self.main_window:
            self.settings.setValue("window_geometry", self.main_window.saveGeometry())
            self.settings.setValue("window_state", self.main_window.saveState())
        
        # Stop autosave timer
        self.autosave_timer.stop()
        
        self.logger.info("Application closing")
        event.accept()
