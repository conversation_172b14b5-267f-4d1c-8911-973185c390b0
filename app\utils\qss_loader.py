"""
QSS (Qt Style Sheet) loader utility.
Loads and processes the application theme stylesheet.
"""

from pathlib import Path
import logging


def load_stylesheet() -> str:
    """
    Load the application stylesheet from theme.qss file.
    
    Returns:
        The stylesheet content as a string
        
    Raises:
        FileNotFoundError: If theme.qss file is not found
        IOError: If there's an error reading the file
    """
    logger = logging.getLogger(__name__)
    
    # Get path to theme file
    theme_path = Path(__file__).parent.parent / "theme.qss"
    
    if not theme_path.exists():
        raise FileNotFoundError(f"Theme file not found: {theme_path}")
    
    try:
        # Read stylesheet content
        with open(theme_path, 'r', encoding='utf-8') as f:
            stylesheet = f.read()
        
        logger.debug(f"Loaded stylesheet from: {theme_path}")
        return stylesheet
        
    except IOError as e:
        logger.error(f"Failed to read stylesheet: {e}")
        raise


def reload_stylesheet() -> str:
    """
    Reload the stylesheet (useful for development).
    
    Returns:
        The reloaded stylesheet content
    """
    return load_stylesheet()
