/* OLED Display UI Designer - Dark Blue Theme */
/* Developed by SK Raiha | SKR Electronics Lab */

/* Color Palette:
   Background (primary): #0B1226
   Panels (secondary): #122040
   Primary accent: #0B3D91
   Cyan accent: #00B4D8
   Muted text: #AFC9FF
   Bright text: #FFFFFF
   Error: #FF5C5C
   Hover/highlight: #6A2AC5
*/

/* Main Application */
QMainWindow {
    background-color: #0B1226;
    color: #FFFFFF;
}

/* Panels and Docks */
QDockWidget {
    background-color: #122040;
    color: #FFFFFF;
    titlebar-close-icon: none;
    titlebar-normal-icon: none;
}

QDockWidget::title {
    background-color: #0B3D91;
    color: #FFFFFF;
    padding: 6px;
    font-weight: bold;
}

QWidget {
    background-color: #122040;
    color: #AFC9FF;
    selection-background-color: #6A2AC5;
    selection-color: #FFFFFF;
}

/* Menu Bar */
QMenuBar {
    background-color: #0B1226;
    color: #FFFFFF;
    border-bottom: 1px solid #0B3D91;
}

QMenuBar::item {
    background-color: transparent;
    padding: 6px 12px;
}

QMenuBar::item:selected {
    background-color: #0B3D91;
}

QMenuBar::item:pressed {
    background-color: #6A2AC5;
}

/* Menus */
QMenu {
    background-color: #122040;
    color: #FFFFFF;
    border: 1px solid #0B3D91;
}

QMenu::item {
    padding: 6px 24px;
}

QMenu::item:selected {
    background-color: #0B3D91;
}

QMenu::separator {
    height: 1px;
    background-color: #0B3D91;
    margin: 2px 0px;
}

/* Tool Bar */
QToolBar {
    background-color: #0B1226;
    border: none;
    spacing: 2px;
    padding: 4px;
}

QToolButton {
    background-color: #122040;
    color: #FFFFFF;
    border: 1px solid #0B3D91;
    border-radius: 4px;
    padding: 6px;
    min-width: 24px;
    min-height: 24px;
}

QToolButton:hover {
    background-color: #0B3D91;
}

QToolButton:pressed {
    background-color: #6A2AC5;
}

QToolButton:checked {
    background-color: #00B4D8;
    color: #000000;
}

/* Buttons */
QPushButton {
    background-color: #0B3D91;
    color: #FFFFFF;
    border: none;
    border-radius: 4px;
    padding: 8px 16px;
    font-weight: bold;
}

QPushButton:hover {
    background-color: #00B4D8;
    color: #000000;
}

QPushButton:pressed {
    background-color: #6A2AC5;
}

QPushButton:disabled {
    background-color: #2A2A2A;
    color: #666666;
}

/* Input Fields */
QLineEdit, QTextEdit, QPlainTextEdit {
    background-color: #0B1226;
    color: #FFFFFF;
    border: 2px solid #0B3D91;
    border-radius: 4px;
    padding: 6px;
}

QLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus {
    border-color: #00B4D8;
}

/* Combo Boxes */
QComboBox {
    background-color: #0B1226;
    color: #FFFFFF;
    border: 2px solid #0B3D91;
    border-radius: 4px;
    padding: 6px;
    min-width: 100px;
}

QComboBox:hover {
    border-color: #00B4D8;
}

QComboBox::drop-down {
    border: none;
    width: 20px;
}

QComboBox::down-arrow {
    image: none;
    border-left: 5px solid transparent;
    border-right: 5px solid transparent;
    border-top: 5px solid #AFC9FF;
}

QComboBox QAbstractItemView {
    background-color: #122040;
    color: #FFFFFF;
    border: 1px solid #0B3D91;
    selection-background-color: #0B3D91;
}

/* Spin Boxes */
QSpinBox, QDoubleSpinBox {
    background-color: #0B1226;
    color: #FFFFFF;
    border: 2px solid #0B3D91;
    border-radius: 4px;
    padding: 6px;
}

QSpinBox:focus, QDoubleSpinBox:focus {
    border-color: #00B4D8;
}

/* Sliders */
QSlider::groove:horizontal {
    background-color: #0B1226;
    height: 6px;
    border-radius: 3px;
}

QSlider::handle:horizontal {
    background-color: #00B4D8;
    width: 16px;
    height: 16px;
    border-radius: 8px;
    margin: -5px 0;
}

QSlider::handle:horizontal:hover {
    background-color: #6A2AC5;
}

/* Check Boxes */
QCheckBox {
    color: #AFC9FF;
    spacing: 8px;
}

QCheckBox::indicator {
    width: 16px;
    height: 16px;
    border: 2px solid #0B3D91;
    border-radius: 3px;
    background-color: #0B1226;
}

QCheckBox::indicator:checked {
    background-color: #00B4D8;
    border-color: #00B4D8;
}

QCheckBox::indicator:hover {
    border-color: #6A2AC5;
}

/* Radio Buttons */
QRadioButton {
    color: #AFC9FF;
    spacing: 8px;
}

QRadioButton::indicator {
    width: 16px;
    height: 16px;
    border: 2px solid #0B3D91;
    border-radius: 8px;
    background-color: #0B1226;
}

QRadioButton::indicator:checked {
    background-color: #00B4D8;
    border-color: #00B4D8;
}

/* Group Boxes */
QGroupBox {
    color: #FFFFFF;
    border: 2px solid #0B3D91;
    border-radius: 6px;
    margin-top: 12px;
    font-weight: bold;
}

QGroupBox::title {
    subcontrol-origin: margin;
    left: 10px;
    padding: 0 8px 0 8px;
    background-color: #122040;
}

/* Tab Widget */
QTabWidget::pane {
    border: 1px solid #0B3D91;
    background-color: #122040;
}

QTabBar::tab {
    background-color: #0B1226;
    color: #AFC9FF;
    border: 1px solid #0B3D91;
    padding: 8px 16px;
    margin-right: 2px;
}

QTabBar::tab:selected {
    background-color: #0B3D91;
    color: #FFFFFF;
}

QTabBar::tab:hover {
    background-color: #6A2AC5;
    color: #FFFFFF;
}

/* Scroll Bars */
QScrollBar:vertical {
    background-color: #0B1226;
    width: 16px;
    border: none;
}

QScrollBar::handle:vertical {
    background-color: #0B3D91;
    border-radius: 8px;
    min-height: 20px;
}

QScrollBar::handle:vertical:hover {
    background-color: #00B4D8;
}

QScrollBar:horizontal {
    background-color: #0B1226;
    height: 16px;
    border: none;
}

QScrollBar::handle:horizontal {
    background-color: #0B3D91;
    border-radius: 8px;
    min-width: 20px;
}

QScrollBar::handle:horizontal:hover {
    background-color: #00B4D8;
}

/* Status Bar */
QStatusBar {
    background-color: #0B1226;
    color: #AFC9FF;
    border-top: 1px solid #0B3D91;
}

/* Splitters */
QSplitter::handle {
    background-color: #0B3D91;
}

QSplitter::handle:horizontal {
    width: 3px;
}

QSplitter::handle:vertical {
    height: 3px;
}

QSplitter::handle:hover {
    background-color: #00B4D8;
}

/* Progress Bar */
QProgressBar {
    background-color: #0B1226;
    border: 2px solid #0B3D91;
    border-radius: 4px;
    text-align: center;
    color: #FFFFFF;
}

QProgressBar::chunk {
    background-color: #00B4D8;
    border-radius: 2px;
}

/* List Widget */
QListWidget {
    background-color: #0B1226;
    color: #AFC9FF;
    border: 1px solid #0B3D91;
    alternate-background-color: #122040;
}

QListWidget::item {
    padding: 6px;
    border-bottom: 1px solid #0B3D91;
}

QListWidget::item:selected {
    background-color: #0B3D91;
    color: #FFFFFF;
}

QListWidget::item:hover {
    background-color: #6A2AC5;
    color: #FFFFFF;
}

/* Tree Widget */
QTreeWidget {
    background-color: #0B1226;
    color: #AFC9FF;
    border: 1px solid #0B3D91;
    alternate-background-color: #122040;
}

QTreeWidget::item {
    padding: 4px;
}

QTreeWidget::item:selected {
    background-color: #0B3D91;
    color: #FFFFFF;
}

QTreeWidget::item:hover {
    background-color: #6A2AC5;
    color: #FFFFFF;
}

/* Tool Tips */
QToolTip {
    background-color: #122040;
    color: #FFFFFF;
    border: 1px solid #00B4D8;
    border-radius: 4px;
    padding: 6px;
    font-size: 12px;
}
