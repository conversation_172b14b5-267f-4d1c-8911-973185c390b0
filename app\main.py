"""
Main entry point for OLED Display UI Designer application.
Handles application startup, exception handling, and logging setup.
"""

import sys
import logging
from pathlib import Path
from PySide6.QtWidgets import QApplication, QMessageBox
from PySide6.QtCore import Qt, QDir
from PySide6.QtGui import QIcon

from app.app import <PERSON><PERSON><PERSON><PERSON><PERSON>erApp
from app.utils.logging_setup import setup_logging


def main():
    """Main application entry point."""
    # Setup logging first
    setup_logging()
    logger = logging.getLogger(__name__)
    
    try:
        # Create QApplication with high DPI support
        QApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)
        QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
        
        app = QApplication(sys.argv)
        app.setApplicationName("OLED Display UI Designer")
        app.setApplicationVersion("1.0.0")
        app.setOrganizationName("SKR Electronics Lab")
        app.setOrganizationDomain("skrelectronicslab.com")
        
        # Set application icon
        icon_path = Path(__file__).parent / "resources" / "app_icon.ico"
        if icon_path.exists():
            app.setWindowIcon(QIcon(str(icon_path)))
        
        # Create and show main application
        designer_app = OLEDDesignerApp()
        designer_app.show()
        
        # Handle command line arguments (e.g., opening .skrui files)
        if len(sys.argv) > 1:
            file_path = Path(sys.argv[1])
            if file_path.exists() and file_path.suffix.lower() == '.skrui':
                designer_app.open_project(str(file_path))
        
        logger.info("Application started successfully")
        return app.exec()
        
    except Exception as e:
        logger.exception("Fatal error during application startup")
        
        # Show error dialog if possible
        try:
            QMessageBox.critical(
                None,
                "Fatal Error",
                f"A fatal error occurred during startup:\n\n{str(e)}\n\n"
                f"Please check the log file for more details."
            )
        except:
            pass
        
        return 1


if __name__ == "__main__":
    sys.exit(main())
